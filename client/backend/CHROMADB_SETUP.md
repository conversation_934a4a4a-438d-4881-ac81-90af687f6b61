# ChromaDB连接问题解决方案

## 问题描述

当调用 `/api/v1/chat/stream` 接口时，出现以下错误：
```
Failed to initialize ChromaDB client: Could not connect to a Chroma server. Are you sure it is running?
```

## 问题原因

1. 应用配置了远程ChromaDB服务器（155.138.220.75:8000），但该服务器无法访问
2. AutoGen的记忆服务需要ChromaDB来存储向量数据
3. 没有本地ChromaDB服务器运行

## 解决方案

### 方案1：启动本地ChromaDB服务器（推荐）

#### 使用Python脚本启动
```bash
# 检查ChromaDB连接状态
python check_chromadb.py

# 启动ChromaDB服务器
python start_chromadb.py
```

#### 使用Shell脚本启动
```bash
# 给脚本执行权限
chmod +x start_chromadb.sh

# 启动服务器
./start_chromadb.sh
```

#### 手动启动
```bash
# 安装ChromaDB（如果未安装）
pip install chromadb

# 创建数据目录
mkdir -p chromadb_data

# 启动服务器
python -m chromadb.cli.cli run --host localhost --port 8000 --path ./chromadb_data
```

### 方案2：使用Docker启动ChromaDB

#### 使用Docker Compose（推荐）
```bash
# 启动ChromaDB服务
docker-compose -f docker-compose.chromadb.yml up -d

# 查看日志
docker-compose -f docker-compose.chromadb.yml logs -f

# 停止服务
docker-compose -f docker-compose.chromadb.yml down
```

#### 使用Docker直接启动
```bash
# 创建数据目录
mkdir -p chromadb_data

# 启动ChromaDB容器
docker run -d \
  --name chromadb-server \
  -p 8000:8000 \
  -v $(pwd)/chromadb_data:/chroma/chroma \
  -e ANONYMIZED_TELEMETRY=False \
  chromadb/chroma:latest

# 查看日志
docker logs -f chromadb-server

# 停止容器
docker stop chromadb-server
docker rm chromadb-server
```

### 方案3：修改配置使用本地存储

如果不想启动ChromaDB服务器，可以修改代码使用本地文件存储：

1. 修改 `c_app/services/memory_service.py` 中的 `VectorMemoryService.__init__` 方法：
```python
# 将这行
self.memory = self._get_memory_http()
# 改为
self.memory = self._get_memory()
```

## 验证解决方案

### 1. 检查ChromaDB服务器状态
```bash
# 使用诊断工具
python check_chromadb.py

# 或手动检查
curl http://localhost:8000/api/v1/heartbeat
```

### 2. 测试API接口
```bash
# 启动应用服务器
python -m uvicorn c_app.main:app --host 0.0.0.0 --port 8001 --reload

# 测试聊天接口
curl -X POST "http://localhost:8001/api/v1/chat/stream" \
  -H "Content-Type: application/json" \
  -d '{
    "messages": [{"role": "user", "content": "你好"}],
    "user_id": "test_user"
  }'
```

## 配置说明

当前配置（`.env`文件）：
```env
CHROMA_DB_HOST=localhost
CHROMA_DB_PORT=8000
CHROMA_DB_SSL=False
```

## 常见问题

### Q: ChromaDB服务器启动失败
A: 检查端口8000是否被占用：
```bash
lsof -i :8000
# 如果被占用，可以杀死进程或使用其他端口
```

### Q: Docker启动失败
A: 确保Docker已安装并运行：
```bash
docker --version
docker info
```

### Q: 权限问题
A: 确保数据目录有写权限：
```bash
chmod 755 chromadb_data
```

## 日志位置

- ChromaDB服务器日志：控制台输出
- 应用日志：`client/logs/chat/memory.log`
- Docker日志：`docker logs chromadb-server`

## 技术支持

如果问题仍然存在，请：
1. 运行 `python check_chromadb.py` 获取详细诊断信息
2. 检查防火墙设置
3. 确认网络连接正常
