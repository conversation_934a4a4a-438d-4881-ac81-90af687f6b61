#!/usr/bin/env python3
"""
启动ChromaDB服务器脚本
用于解决ChromaDB连接问题
"""

import os
import sys
import subprocess
import time
import requests
from pathlib import Path

def check_chromadb_installed():
    """检查ChromaDB是否已安装"""
    try:
        import chromadb
        print("✓ ChromaDB已安装")
        return True
    except ImportError:
        print("✗ ChromaDB未安装")
        return False

def install_chromadb():
    """安装ChromaDB"""
    print("正在安装ChromaDB...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "chromadb"])
        print("✓ ChromaDB安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"✗ ChromaDB安装失败: {e}")
        return False

def start_chromadb_server(host="localhost", port=8002, persist_directory=None):
    """启动ChromaDB服务器"""
    if persist_directory is None:
        # 使用项目目录下的chromadb_data作为持久化目录
        persist_directory = Path(__file__).parent / "chromadb_data"
        persist_directory.mkdir(exist_ok=True)
    
    print(f"正在启动ChromaDB服务器...")
    print(f"主机: {host}")
    print(f"端口: {port}")
    print(f"数据目录: {persist_directory}")
    
    try:
        # 启动ChromaDB服务器
        cmd = [
            sys.executable, "-m", "chromadb.cli.cli", "run",
            "--host", host,
            "--port", str(port),
            "--path", str(persist_directory)
        ]
        
        print(f"执行命令: {' '.join(cmd)}")
        process = subprocess.Popen(cmd)
        
        # 等待服务器启动
        print("等待服务器启动...")
        for i in range(30):  # 最多等待30秒
            try:
                response = requests.get(f"http://{host}:{port}/api/v1/heartbeat", timeout=2)
                if response.status_code == 200:
                    print(f"✓ ChromaDB服务器启动成功！")
                    print(f"  访问地址: http://{host}:{port}")
                    print(f"  数据目录: {persist_directory}")
                    return process
            except requests.exceptions.RequestException:
                pass
            
            time.sleep(1)
            print(f"等待中... ({i+1}/30)")
        
        print("✗ 服务器启动超时")
        process.terminate()
        return None
        
    except Exception as e:
        print(f"✗ 启动ChromaDB服务器失败: {e}")
        return None

def check_server_running(host="localhost", port=8001):
    """检查ChromaDB服务器是否正在运行"""
    try:
        response = requests.get(f"http://{host}:{port}/api/v1/heartbeat", timeout=2)
        return response.status_code == 200
    except requests.exceptions.RequestException:
        return False

def main():
    """主函数"""
    print("ChromaDB服务器启动工具")
    print("=" * 50)
    
    # 检查ChromaDB是否已安装
    if not check_chromadb_installed():
        if input("是否安装ChromaDB? (y/n): ").lower() == 'y':
            if not install_chromadb():
                sys.exit(1)
        else:
            print("需要安装ChromaDB才能继续")
            sys.exit(1)
    
    # 检查服务器是否已经在运行
    if check_server_running():
        print("✓ ChromaDB服务器已经在运行")
        return
    
    # 启动服务器
    process = start_chromadb_server()
    if process:
        try:
            print("\n按 Ctrl+C 停止服务器")
            process.wait()
        except KeyboardInterrupt:
            print("\n正在停止服务器...")
            process.terminate()
            process.wait()
            print("✓ 服务器已停止")
    else:
        sys.exit(1)

if __name__ == "__main__":
    main()
