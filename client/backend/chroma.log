[32mIN<PERSON>O[0m:     [10-07-2025 18:02:51] Set chroma_server_nofile to 65535
[32mINFO[0m:     [10-07-2025 18:02:52] Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
[36mDEBUG[0m:    [10-07-2025 18:02:52] Starting component System
[36mDEBUG[0m:    [10-07-2025 18:02:52] Starting component OpenTelemetryClient
[36mDEBUG[0m:    [10-07-2025 18:02:52] Starting component SqliteDB
[36mDEBUG[0m:    [10-07-2025 18:02:52] Starting component SimpleQuotaEnforcer
[36mDEBUG[0m:    [10-07-2025 18:02:52] Starting component Posthog
[36mDEBUG[0m:    [10-07-2025 18:02:52] Starting component SimpleRateLimitEnforcer
[36mDEBUG[0m:    [10-07-2025 18:02:52] Starting component LocalSegmentManager
[36mDEBUG[0m:    [10-07-2025 18:02:52] Starting component LocalExecutor
[36mDEBUG[0m:    [10-07-2025 18:02:52] Starting component SegmentAPI
[36mDEBUG[0m:    [10-07-2025 18:02:52] Starting component SimpleAsyncRateLimitEnforcer
[31mERROR[0m:    [10-07-2025 18:02:52] Failed to send telemetry event ServerStartEvent: capture() takes 1 positional argument but 3 were given
[32mINFO[0m:     [10-07-2025 18:02:52] Started server process [[36m3728[0m]
[32mINFO[0m:     [10-07-2025 18:02:52] Waiting for application startup.
[32mINFO[0m:     [10-07-2025 18:02:52] Application startup complete.
[31mERROR[0m:    [10-07-2025 18:02:52] [Errno 48] error while attempting to bind on address ('127.0.0.1', 8000): address already in use
[32mINFO[0m:     [10-07-2025 18:02:52] Waiting for application shutdown.
[36mDEBUG[0m:    [10-07-2025 18:02:52] Stopping component System
[36mDEBUG[0m:    [10-07-2025 18:02:52] Stopping component SegmentAPI
[36mDEBUG[0m:    [10-07-2025 18:02:52] Stopping component LocalExecutor
[36mDEBUG[0m:    [10-07-2025 18:02:52] Stopping component LocalSegmentManager
[36mDEBUG[0m:    [10-07-2025 18:02:52] Stopping component SimpleAsyncRateLimitEnforcer
[36mDEBUG[0m:    [10-07-2025 18:02:52] Stopping component SimpleRateLimitEnforcer
[36mDEBUG[0m:    [10-07-2025 18:02:52] Stopping component Posthog
[36mDEBUG[0m:    [10-07-2025 18:02:52] Stopping component SimpleQuotaEnforcer
[36mDEBUG[0m:    [10-07-2025 18:02:52] Stopping component SqliteDB
[36mDEBUG[0m:    [10-07-2025 18:02:52] Stopping component OpenTelemetryClient
[32mINFO[0m:     [10-07-2025 18:02:52] Application shutdown complete.
[32mINFO[0m:     [10-07-2025 18:04:31] Set chroma_server_nofile to 65535
[32mINFO[0m:     [10-07-2025 18:04:31] Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
[36mDEBUG[0m:    [10-07-2025 18:04:31] Starting component System
[36mDEBUG[0m:    [10-07-2025 18:04:31] Starting component OpenTelemetryClient
[36mDEBUG[0m:    [10-07-2025 18:04:31] Starting component SqliteDB
[36mDEBUG[0m:    [10-07-2025 18:04:31] Starting component SimpleQuotaEnforcer
[36mDEBUG[0m:    [10-07-2025 18:04:31] Starting component Posthog
[36mDEBUG[0m:    [10-07-2025 18:04:31] Starting component SimpleRateLimitEnforcer
[36mDEBUG[0m:    [10-07-2025 18:04:31] Starting component LocalSegmentManager
[36mDEBUG[0m:    [10-07-2025 18:04:31] Starting component LocalExecutor
[36mDEBUG[0m:    [10-07-2025 18:04:31] Starting component SegmentAPI
[36mDEBUG[0m:    [10-07-2025 18:04:31] Starting component SimpleAsyncRateLimitEnforcer
[31mERROR[0m:    [10-07-2025 18:04:31] Failed to send telemetry event ServerStartEvent: capture() takes 1 positional argument but 3 were given
[32mINFO[0m:     [10-07-2025 18:04:31] Started server process [[36m3760[0m]
[32mINFO[0m:     [10-07-2025 18:04:31] Waiting for application startup.
[32mINFO[0m:     [10-07-2025 18:04:31] Application startup complete.
[31mERROR[0m:    [10-07-2025 18:04:31] [Errno 48] error while attempting to bind on address ('127.0.0.1', 8001): address already in use
[32mINFO[0m:     [10-07-2025 18:04:31] Waiting for application shutdown.
[36mDEBUG[0m:    [10-07-2025 18:04:31] Stopping component System
[36mDEBUG[0m:    [10-07-2025 18:04:31] Stopping component SegmentAPI
[36mDEBUG[0m:    [10-07-2025 18:04:31] Stopping component LocalExecutor
[36mDEBUG[0m:    [10-07-2025 18:04:31] Stopping component LocalSegmentManager
[36mDEBUG[0m:    [10-07-2025 18:04:31] Stopping component SimpleAsyncRateLimitEnforcer
[36mDEBUG[0m:    [10-07-2025 18:04:31] Stopping component SimpleRateLimitEnforcer
[36mDEBUG[0m:    [10-07-2025 18:04:31] Stopping component Posthog
[36mDEBUG[0m:    [10-07-2025 18:04:31] Stopping component SimpleQuotaEnforcer
[36mDEBUG[0m:    [10-07-2025 18:04:31] Stopping component SqliteDB
[36mDEBUG[0m:    [10-07-2025 18:04:31] Stopping component OpenTelemetryClient
[32mINFO[0m:     [10-07-2025 18:04:31] Application shutdown complete.
[32mINFO[0m:     [10-07-2025 18:05:17] Set chroma_server_nofile to 65535
[32mINFO[0m:     [10-07-2025 18:05:18] Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
[36mDEBUG[0m:    [10-07-2025 18:05:18] Starting component System
[36mDEBUG[0m:    [10-07-2025 18:05:18] Starting component OpenTelemetryClient
[36mDEBUG[0m:    [10-07-2025 18:05:18] Starting component SqliteDB
[36mDEBUG[0m:    [10-07-2025 18:05:18] Starting component SimpleQuotaEnforcer
[36mDEBUG[0m:    [10-07-2025 18:05:18] Starting component Posthog
[36mDEBUG[0m:    [10-07-2025 18:05:18] Starting component SimpleRateLimitEnforcer
[36mDEBUG[0m:    [10-07-2025 18:05:18] Starting component LocalSegmentManager
[36mDEBUG[0m:    [10-07-2025 18:05:18] Starting component LocalExecutor
[36mDEBUG[0m:    [10-07-2025 18:05:18] Starting component SegmentAPI
[36mDEBUG[0m:    [10-07-2025 18:05:18] Starting component SimpleAsyncRateLimitEnforcer
[31mERROR[0m:    [10-07-2025 18:05:18] Failed to send telemetry event ServerStartEvent: capture() takes 1 positional argument but 3 were given
[32mINFO[0m:     [10-07-2025 18:05:18] Started server process [[36m3776[0m]
[32mINFO[0m:     [10-07-2025 18:05:18] Waiting for application startup.
[32mINFO[0m:     [10-07-2025 18:05:18] Application startup complete.
[32mINFO[0m:     [10-07-2025 18:05:18] Uvicorn running on [1mhttp://localhost:8002[0m (Press CTRL+C to quit)
[32mINFO[0m:     [10-07-2025 18:05:44] ::1:64061 - "GET /api/v1/heartbeat HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:05:45] ::1:64062 - "GET /api/v2/auth/identity HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:05:45] ::1:64063 - "GET /api/v2/tenants/default_tenant HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:05:45] ::1:64063 - "GET /api/v2/tenants/default_tenant/databases/default_database HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:05:45] ::1:64062 - "GET /api/v2/heartbeat HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:05:45] ::1:64062 - "GET /api/v2/tenants/default_tenant/databases/default_database/collections HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:06:20] ::1:64111 - "GET /api/v2/auth/identity HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:06:20] ::1:64112 - "GET /api/v2/tenants/default_tenant HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:06:20] ::1:64112 - "GET /api/v2/tenants/default_tenant/databases/default_database HTTP/1.1" 200
[31mERROR[0m:    [10-07-2025 18:06:20] Failed to send telemetry event ClientCreateCollectionEvent: capture() takes 1 positional argument but 3 were given
[32mINFO[0m:     [10-07-2025 18:06:20] ::1:64111 - "POST /api/v2/tenants/default_tenant/databases/default_database/collections HTTP/1.1" 200
[31mERROR[0m:    [10-07-2025 18:06:22] Failed to send telemetry event CollectionQueryEvent: capture() takes 1 positional argument but 3 were given
[36mDEBUG[0m:    [10-07-2025 18:06:22] Starting component PersistentLocalHnswSegment
[32mINFO[0m:     [10-07-2025 18:06:22] ::1:64111 - "POST /api/v2/tenants/default_tenant/databases/default_database/collections/79b0d3f0-905e-45e7-9fe5-7cef9926ecdb/query HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:06:22] ::1:64113 - "GET /api/v2/auth/identity HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:06:22] ::1:64114 - "GET /api/v2/tenants/default_tenant HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:06:22] ::1:64114 - "GET /api/v2/tenants/default_tenant/databases/default_database HTTP/1.1" 200
[31mERROR[0m:    [10-07-2025 18:06:22] Failed to send telemetry event ClientCreateCollectionEvent: capture() takes 1 positional argument but 3 were given
[32mINFO[0m:     [10-07-2025 18:06:22] ::1:64113 - "POST /api/v2/tenants/default_tenant/databases/default_database/collections HTTP/1.1" 200
[31mERROR[0m:    [10-07-2025 18:06:23] Failed to send telemetry event CollectionQueryEvent: capture() takes 1 positional argument but 3 were given
[36mDEBUG[0m:    [10-07-2025 18:06:23] Starting component PersistentLocalHnswSegment
[32mINFO[0m:     [10-07-2025 18:06:23] ::1:64113 - "POST /api/v2/tenants/default_tenant/databases/default_database/collections/8508380d-f058-4088-9434-1b33bc284e0b/query HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:08:12] ::1:64235 - "GET /api/v2/auth/identity HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:08:12] ::1:64236 - "GET /api/v2/tenants/default_tenant HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:08:12] ::1:64236 - "GET /api/v2/tenants/default_tenant/databases/default_database HTTP/1.1" 200
[36mDEBUG[0m:    [10-07-2025 18:08:12] Collection customer_service_public_memory already exists, returning existing collection.
[31mERROR[0m:    [10-07-2025 18:08:12] Failed to send telemetry event ClientCreateCollectionEvent: capture() takes 1 positional argument but 3 were given
[32mINFO[0m:     [10-07-2025 18:08:12] ::1:64235 - "POST /api/v2/tenants/default_tenant/databases/default_database/collections HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:08:14] ::1:64235 - "POST /api/v2/tenants/default_tenant/databases/default_database/collections/79b0d3f0-905e-45e7-9fe5-7cef9926ecdb/query HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:08:14] ::1:64239 - "GET /api/v2/auth/identity HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:08:14] ::1:64240 - "GET /api/v2/tenants/default_tenant HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:08:14] ::1:64240 - "GET /api/v2/tenants/default_tenant/databases/default_database HTTP/1.1" 200
[36mDEBUG[0m:    [10-07-2025 18:08:14] Collection customer_service_1 already exists, returning existing collection.
[31mERROR[0m:    [10-07-2025 18:08:14] Failed to send telemetry event ClientCreateCollectionEvent: capture() takes 1 positional argument but 3 were given
[32mINFO[0m:     [10-07-2025 18:08:14] ::1:64239 - "POST /api/v2/tenants/default_tenant/databases/default_database/collections HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:08:16] ::1:64239 - "POST /api/v2/tenants/default_tenant/databases/default_database/collections/8508380d-f058-4088-9434-1b33bc284e0b/query HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:08:25] ::1:64253 - "POST /api/v2/tenants/default_tenant/databases/default_database/collections/79b0d3f0-905e-45e7-9fe5-7cef9926ecdb/query HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:08:26] ::1:64255 - "POST /api/v2/tenants/default_tenant/databases/default_database/collections/8508380d-f058-4088-9434-1b33bc284e0b/query HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:08:52] ::1:64287 - "POST /api/v2/tenants/default_tenant/databases/default_database/collections/79b0d3f0-905e-45e7-9fe5-7cef9926ecdb/query HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:08:52] ::1:64288 - "POST /api/v2/tenants/default_tenant/databases/default_database/collections/8508380d-f058-4088-9434-1b33bc284e0b/query HTTP/1.1" 200
[32mINFO[0m:     [10-07-2025 18:40:38] Shutting down
[32mINFO[0m:     [10-07-2025 18:40:39] Waiting for application shutdown.
[36mDEBUG[0m:    [10-07-2025 18:40:39] Stopping component System
[36mDEBUG[0m:    [10-07-2025 18:40:39] Stopping component SegmentAPI
[36mDEBUG[0m:    [10-07-2025 18:40:39] Stopping component LocalExecutor
[36mDEBUG[0m:    [10-07-2025 18:40:39] Stopping component PersistentLocalHnswSegment
[36mDEBUG[0m:    [10-07-2025 18:40:39] Stopping component PersistentLocalHnswSegment
[36mDEBUG[0m:    [10-07-2025 18:40:39] Stopping component LocalSegmentManager
[36mDEBUG[0m:    [10-07-2025 18:40:39] Stopping component SimpleAsyncRateLimitEnforcer
[36mDEBUG[0m:    [10-07-2025 18:40:39] Stopping component SimpleRateLimitEnforcer
[36mDEBUG[0m:    [10-07-2025 18:40:39] Stopping component Posthog
[36mDEBUG[0m:    [10-07-2025 18:40:39] Stopping component SimpleQuotaEnforcer
[36mDEBUG[0m:    [10-07-2025 18:40:39] Stopping component SqliteDB
[36mDEBUG[0m:    [10-07-2025 18:40:39] Stopping component OpenTelemetryClient
[32mINFO[0m:     [10-07-2025 18:40:39] Application shutdown complete.
[32mINFO[0m:     [10-07-2025 18:40:39] Finished server process [[36m3776[0m]
INFO:     [11-07-2025 13:39:17] Set chroma_server_nofile to 65535
INFO:     [11-07-2025 13:39:17] Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
DEBUG:    [11-07-2025 13:39:17] Starting component System
DEBUG:    [11-07-2025 13:39:17] Starting component OpenTelemetryClient
DEBUG:    [11-07-2025 13:39:17] Starting component SqliteDB
DEBUG:    [11-07-2025 13:39:17] Starting component SimpleQuotaEnforcer
DEBUG:    [11-07-2025 13:39:17] Starting component Posthog
DEBUG:    [11-07-2025 13:39:17] Starting component SimpleRateLimitEnforcer
DEBUG:    [11-07-2025 13:39:17] Starting component LocalSegmentManager
DEBUG:    [11-07-2025 13:39:17] Starting component LocalExecutor
DEBUG:    [11-07-2025 13:39:17] Starting component SegmentAPI
DEBUG:    [11-07-2025 13:39:17] Starting component SimpleAsyncRateLimitEnforcer
ERROR:    [11-07-2025 13:39:18] Failed to send telemetry event ServerStartEvent: capture() takes 1 positional argument but 3 were given
INFO:     [11-07-2025 13:39:18] Started server process [9469]
INFO:     [11-07-2025 13:39:18] Waiting for application startup.
INFO:     [11-07-2025 13:39:18] Application startup complete.
INFO:     [11-07-2025 13:39:18] Uvicorn running on http://localhost:8001 (Press CTRL+C to quit)
INFO:     [11-07-2025 13:39:18] ::1:51748 - "GET /api/v1/heartbeat HTTP/1.1" 200
INFO:     [11-07-2025 13:41:05] Shutting down
INFO:     [11-07-2025 13:41:05] Waiting for application shutdown.
DEBUG:    [11-07-2025 13:41:05] Stopping component System
DEBUG:    [11-07-2025 13:41:05] Stopping component SegmentAPI
DEBUG:    [11-07-2025 13:41:05] Stopping component LocalExecutor
DEBUG:    [11-07-2025 13:41:05] Stopping component LocalSegmentManager
DEBUG:    [11-07-2025 13:41:05] Stopping component SimpleAsyncRateLimitEnforcer
DEBUG:    [11-07-2025 13:41:05] Stopping component SimpleRateLimitEnforcer
DEBUG:    [11-07-2025 13:41:05] Stopping component Posthog
DEBUG:    [11-07-2025 13:41:05] Stopping component SimpleQuotaEnforcer
DEBUG:    [11-07-2025 13:41:05] Stopping component SqliteDB
DEBUG:    [11-07-2025 13:41:05] Stopping component OpenTelemetryClient
INFO:     [11-07-2025 13:41:05] Application shutdown complete.
INFO:     [11-07-2025 13:41:05] Finished server process [9469]
INFO:     [11-07-2025 13:41:21] Set chroma_server_nofile to 65535
INFO:     [11-07-2025 13:41:21] Anonymized telemetry enabled. See                     https://docs.trychroma.com/telemetry for more information.
DEBUG:    [11-07-2025 13:41:21] Starting component System
DEBUG:    [11-07-2025 13:41:21] Starting component OpenTelemetryClient
DEBUG:    [11-07-2025 13:41:21] Starting component SqliteDB
DEBUG:    [11-07-2025 13:41:21] Starting component SimpleQuotaEnforcer
DEBUG:    [11-07-2025 13:41:21] Starting component Posthog
DEBUG:    [11-07-2025 13:41:21] Starting component SimpleRateLimitEnforcer
DEBUG:    [11-07-2025 13:41:21] Starting component LocalSegmentManager
DEBUG:    [11-07-2025 13:41:21] Starting component LocalExecutor
DEBUG:    [11-07-2025 13:41:21] Starting component SegmentAPI
DEBUG:    [11-07-2025 13:41:21] Starting component SimpleAsyncRateLimitEnforcer
ERROR:    [11-07-2025 13:41:21] Failed to send telemetry event ServerStartEvent: capture() takes 1 positional argument but 3 were given
INFO:     [11-07-2025 13:41:21] Started server process [9517]
INFO:     [11-07-2025 13:41:21] Waiting for application startup.
INFO:     [11-07-2025 13:41:21] Application startup complete.
INFO:     [11-07-2025 13:41:21] Uvicorn running on http://localhost:8002 (Press CTRL+C to quit)
INFO:     [11-07-2025 13:41:22] ::1:52097 - "GET /api/v1/heartbeat HTTP/1.1" 200
INFO:     [11-07-2025 13:41:31] ::1:52102 - "GET /api/v1/heartbeat HTTP/1.1" 200
INFO:     [11-07-2025 13:41:32] ::1:52103 - "GET /api/v2/auth/identity HTTP/1.1" 200
INFO:     [11-07-2025 13:41:32] ::1:52104 - "GET /api/v2/tenants/default_tenant HTTP/1.1" 200
INFO:     [11-07-2025 13:41:32] ::1:52104 - "GET /api/v2/tenants/default_tenant/databases/default_database HTTP/1.1" 200
INFO:     [11-07-2025 13:41:32] ::1:52103 - "GET /api/v2/heartbeat HTTP/1.1" 200
INFO:     [11-07-2025 13:41:32] ::1:52103 - "GET /api/v2/tenants/default_tenant/databases/default_database/collections HTTP/1.1" 200
