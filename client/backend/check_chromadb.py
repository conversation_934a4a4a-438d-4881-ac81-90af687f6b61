#!/usr/bin/env python3
"""
ChromaDB连接检查工具
用于诊断和解决ChromaDB连接问题
"""

import os
import sys
import requests
import time
from pathlib import Path

# 添加项目路径到sys.path
sys.path.append(str(Path(__file__).parent))

from c_app.core.config import settings

def check_chromadb_server(host, port, timeout=5):
    """检查ChromaDB服务器是否可访问"""
    try:
        url = f"http://{host}:{port}/api/v1/heartbeat"
        print(f"检查ChromaDB服务器: {url}")
        
        response = requests.get(url, timeout=timeout)
        if response.status_code == 200:
            print(f"✓ ChromaDB服务器运行正常")
            print(f"  响应时间: {response.elapsed.total_seconds():.3f}秒")
            return True
        else:
            print(f"✗ ChromaDB服务器响应异常: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectTimeout:
        print(f"✗ 连接超时 ({timeout}秒)")
        return False
    except requests.exceptions.ConnectionError:
        print(f"✗ 连接失败 - 服务器可能未启动")
        return False
    except Exception as e:
        print(f"✗ 检查失败: {e}")
        return False

def test_chromadb_client():
    """测试ChromaDB客户端连接"""
    try:
        import chromadb
        from chromadb.config import Settings as ChromaSettings
        
        print("测试ChromaDB客户端连接...")
        
        # 尝试连接到HTTP服务器
        try:
            client = chromadb.HttpClient(
                host=settings.CHROMA_DB_HOST,
                port=settings.CHROMA_DB_PORT,
                settings=ChromaSettings(anonymized_telemetry=False)
            )
            
            # 测试心跳
            client.heartbeat()
            print(f"✓ ChromaDB客户端连接成功")
            
            # 列出集合
            collections = client.list_collections()
            print(f"  现有集合数量: {len(collections)}")
            for collection in collections:
                print(f"    - {collection.name}")
            
            return True
            
        except Exception as e:
            print(f"✗ ChromaDB客户端连接失败: {e}")
            return False
            
    except ImportError:
        print("✗ ChromaDB库未安装")
        return False

def test_autogen_memory():
    """测试AutoGen记忆服务"""
    try:
        from autogen_ext.memory.chromadb import ChromaDBVectorMemory, HttpChromaDBVectorMemoryConfig
        
        print("测试AutoGen ChromaDB记忆服务...")
        
        config = HttpChromaDBVectorMemoryConfig(
            host=settings.CHROMA_DB_HOST,
            port=settings.CHROMA_DB_PORT,
            collection_name="test_collection",
            k=5,
            score_threshold=0.4
        )
        
        memory = ChromaDBVectorMemory(config=config)
        print("✓ AutoGen记忆服务初始化成功")
        return True
        
    except Exception as e:
        print(f"✗ AutoGen记忆服务测试失败: {e}")
        return False

def show_config():
    """显示当前配置"""
    print("当前ChromaDB配置:")
    print(f"  主机: {settings.CHROMA_DB_HOST}")
    print(f"  端口: {settings.CHROMA_DB_PORT}")
    print(f"  SSL: {settings.CHROMA_DB_SSL}")

def show_solutions():
    """显示解决方案"""
    print("\n解决方案:")
    print("1. 启动本地ChromaDB服务器:")
    print("   python start_chromadb.py")
    print("   或")
    print("   ./start_chromadb.sh")
    print()
    print("2. 使用Docker启动ChromaDB:")
    print("   docker run -p 8000:8000 chromadb/chroma")
    print()
    print("3. 修改配置使用本地存储:")
    print("   在.env文件中设置:")
    print("   CHROMA_DB_HOST=localhost")
    print("   CHROMA_DB_PORT=8000")

def main():
    """主函数"""
    print("ChromaDB连接诊断工具")
    print("=" * 50)
    
    # 显示配置
    show_config()
    print()
    
    # 检查服务器
    server_ok = check_chromadb_server(settings.CHROMA_DB_HOST, settings.CHROMA_DB_PORT)
    print()
    
    # 测试客户端
    if server_ok:
        client_ok = test_chromadb_client()
        print()
        
        # 测试AutoGen记忆服务
        if client_ok:
            memory_ok = test_autogen_memory()
            print()
            
            if memory_ok:
                print("✓ 所有测试通过！ChromaDB连接正常")
                return
    
    # 如果有问题，显示解决方案
    show_solutions()

if __name__ == "__main__":
    main()
