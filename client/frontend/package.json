{"name": "client-frontend", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@ant-design/nextjs-registry": "^1.0.2", "@heroicons/react": "^2.2.0", "@radix-ui/react-label": "^2.1.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-tooltip": "^1.2.0", "@tailwindcss/typography": "^0.5.16", "@types/react-syntax-highlighter": "^15.5.13", "autoprefixer": "^10.4.17", "axios": "^1.6.5", "chart.js": "^4.4.8", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "danwen-agent-system-frontend": "file:", "framer-motion": "^11.18.2", "highlight.js": "^11.11.1", "lucide-react": "^0.321.0", "next": "^14.2.26", "next-themes": "^0.4.6", "postcss": "^8.4.35", "react": "^18.3.1", "react-chartjs-2": "^5.2.0", "react-dom": "^18.3.1", "react-hot-toast": "^2.5.2", "react-icons": "^5.0.1", "react-markdown": "^9.1.0", "react-syntax-highlighter": "^15.6.1", "rehype-raw": "^7.0.0", "remark-breaks": "^4.0.0", "remark-gfm": "^4.0.1", "tailwind-merge": "^2.2.1", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5.3.3"}, "devDependencies": {"@types/node": "^20.11.5", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "eslint": "^8.56.0", "eslint-config-next": "^14.1.0"}}