2025-07-01 11:29:29,290 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-01 11:29:29,291 - chat_service - INFO - Created new session 4d2b30c8-d7de-452e-8e34-e82e5aae8cdb for user 1
2025-07-01 11:29:29,714 - chat_service - ERROR - 聊天处理失败 1: Could not connect to a Chroma server. Are you sure it is running?
Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 256, in handle_request
    raise exc from None
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 101, in handle_request
    raise exc
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 78, in handle_request
    stream = self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 124, in _connect
    stream = self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_backends/sync.py", line 207, in connect_tcp
    with map_exceptions(exc_map):
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 61] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 101, in get_user_identity
    return self._server.get_user_identity()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/__init__.py", line 150, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 182, in get_user_identity
    return UserIdentity(**self._make_request("get", "/auth/identity"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 89, in _make_request
    response = self._session.request(method, url, **cast(Any, kwargs))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 825, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 914, in send
    response = self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 249, in handle_request
    with map_httpcore_exceptions():
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 61] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/client/backend/c_app/services/chat_service_v4.py", line 170, in chat_stream
    async for event in agent.run_stream(task=last_user_message):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_base_chat_agent.py", line 202, in run_stream
    async for message in self.on_messages_stream(input_messages, cancellation_token):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 951, in on_messages_stream
    for event_msg in await self._update_model_context_with_memory(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 1055, in _update_model_context_with_memory
    update_context_result = await mem.update_context(model_context)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 342, in update_context
    query_results = await self.query(query_text)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 382, in query
    self._ensure_initialized()
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 212, in _ensure_initialized
    self._client = HttpClient(
                   ^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/__init__.py", line 203, in HttpClient
    return ClientCreator(tenant=tenant, database=database, settings=settings)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 65, in __init__
    user_identity = self.get_user_identity()
                    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 103, in get_user_identity
    raise ValueError(
ValueError: Could not connect to a Chroma server. Are you sure it is running?
2025-07-01 14:46:27,838 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-01 14:46:27,840 - chat_service - INFO - Created new session 4d2b30c8-d7de-452e-8e34-e82e5aae8cdb for user 1
2025-07-01 14:46:28,277 - chat_service - ERROR - 聊天处理失败 1: Could not connect to a Chroma server. Are you sure it is running?
Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 256, in handle_request
    raise exc from None
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 101, in handle_request
    raise exc
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 78, in handle_request
    stream = self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 124, in _connect
    stream = self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_backends/sync.py", line 207, in connect_tcp
    with map_exceptions(exc_map):
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 61] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 101, in get_user_identity
    return self._server.get_user_identity()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/__init__.py", line 150, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 182, in get_user_identity
    return UserIdentity(**self._make_request("get", "/auth/identity"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 89, in _make_request
    response = self._session.request(method, url, **cast(Any, kwargs))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 825, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 914, in send
    response = self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 249, in handle_request
    with map_httpcore_exceptions():
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 61] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/client/backend/c_app/services/chat_service_v4.py", line 170, in chat_stream
    async for event in agent.run_stream(task=last_user_message):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_base_chat_agent.py", line 202, in run_stream
    async for message in self.on_messages_stream(input_messages, cancellation_token):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 951, in on_messages_stream
    for event_msg in await self._update_model_context_with_memory(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 1055, in _update_model_context_with_memory
    update_context_result = await mem.update_context(model_context)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 342, in update_context
    query_results = await self.query(query_text)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 382, in query
    self._ensure_initialized()
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 212, in _ensure_initialized
    self._client = HttpClient(
                   ^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/__init__.py", line 203, in HttpClient
    return ClientCreator(tenant=tenant, database=database, settings=settings)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 65, in __init__
    user_identity = self.get_user_identity()
                    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 103, in get_user_identity
    raise ValueError(
ValueError: Could not connect to a Chroma server. Are you sure it is running?
2025-07-01 14:52:25,861 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-01 14:52:25,863 - chat_service - INFO - Created new session 4d2b30c8-d7de-452e-8e34-e82e5aae8cdb for user 1
2025-07-01 14:52:26,649 - chat_service - ERROR - 聊天处理失败 1: Could not connect to a Chroma server. Are you sure it is running?
Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 256, in handle_request
    raise exc from None
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 101, in handle_request
    raise exc
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 78, in handle_request
    stream = self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 124, in _connect
    stream = self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_backends/sync.py", line 207, in connect_tcp
    with map_exceptions(exc_map):
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 61] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 101, in get_user_identity
    return self._server.get_user_identity()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/__init__.py", line 150, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 182, in get_user_identity
    return UserIdentity(**self._make_request("get", "/auth/identity"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 89, in _make_request
    response = self._session.request(method, url, **cast(Any, kwargs))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 825, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 914, in send
    response = self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 249, in handle_request
    with map_httpcore_exceptions():
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 61] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/client/backend/c_app/services/chat_service_v4.py", line 170, in chat_stream
    async for event in agent.run_stream(task=last_user_message):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_base_chat_agent.py", line 202, in run_stream
    async for message in self.on_messages_stream(input_messages, cancellation_token):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 951, in on_messages_stream
    for event_msg in await self._update_model_context_with_memory(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 1055, in _update_model_context_with_memory
    update_context_result = await mem.update_context(model_context)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 342, in update_context
    query_results = await self.query(query_text)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 382, in query
    self._ensure_initialized()
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 212, in _ensure_initialized
    self._client = HttpClient(
                   ^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/__init__.py", line 203, in HttpClient
    return ClientCreator(tenant=tenant, database=database, settings=settings)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 65, in __init__
    user_identity = self.get_user_identity()
                    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 103, in get_user_identity
    raise ValueError(
ValueError: Could not connect to a Chroma server. Are you sure it is running?
2025-07-01 15:01:47,594 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-01 15:01:47,595 - chat_service - INFO - Created new session 4d2b30c8-d7de-452e-8e34-e82e5aae8cdb for user 1
2025-07-01 15:01:48,233 - chat_service - ERROR - 聊天处理失败 1: Could not connect to a Chroma server. Are you sure it is running?
Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 256, in handle_request
    raise exc from None
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 101, in handle_request
    raise exc
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 78, in handle_request
    stream = self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 124, in _connect
    stream = self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_backends/sync.py", line 207, in connect_tcp
    with map_exceptions(exc_map):
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 61] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 101, in get_user_identity
    return self._server.get_user_identity()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/__init__.py", line 150, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 182, in get_user_identity
    return UserIdentity(**self._make_request("get", "/auth/identity"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 89, in _make_request
    response = self._session.request(method, url, **cast(Any, kwargs))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 825, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 914, in send
    response = self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 249, in handle_request
    with map_httpcore_exceptions():
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 61] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/client/backend/c_app/services/chat_service_v4.py", line 170, in chat_stream
    async for event in agent.run_stream(task=last_user_message):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_base_chat_agent.py", line 202, in run_stream
    async for message in self.on_messages_stream(input_messages, cancellation_token):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 951, in on_messages_stream
    for event_msg in await self._update_model_context_with_memory(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 1055, in _update_model_context_with_memory
    update_context_result = await mem.update_context(model_context)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 342, in update_context
    query_results = await self.query(query_text)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 382, in query
    self._ensure_initialized()
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 212, in _ensure_initialized
    self._client = HttpClient(
                   ^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/__init__.py", line 203, in HttpClient
    return ClientCreator(tenant=tenant, database=database, settings=settings)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 65, in __init__
    user_identity = self.get_user_identity()
                    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 103, in get_user_identity
    raise ValueError(
ValueError: Could not connect to a Chroma server. Are you sure it is running?
2025-07-01 15:10:36,442 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-01 15:10:36,442 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-01 15:10:36,863 - chat_service - ERROR - 聊天处理失败 1: Could not connect to a Chroma server. Are you sure it is running?
Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 256, in handle_request
    raise exc from None
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 101, in handle_request
    raise exc
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 78, in handle_request
    stream = self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 124, in _connect
    stream = self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_backends/sync.py", line 207, in connect_tcp
    with map_exceptions(exc_map):
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 61] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 101, in get_user_identity
    return self._server.get_user_identity()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/__init__.py", line 150, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 182, in get_user_identity
    return UserIdentity(**self._make_request("get", "/auth/identity"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 89, in _make_request
    response = self._session.request(method, url, **cast(Any, kwargs))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 825, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 914, in send
    response = self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 249, in handle_request
    with map_httpcore_exceptions():
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 61] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/client/backend/c_app/services/chat_service_v4.py", line 170, in chat_stream
    async for event in agent.run_stream(task=last_user_message):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_base_chat_agent.py", line 202, in run_stream
    async for message in self.on_messages_stream(input_messages, cancellation_token):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 951, in on_messages_stream
    for event_msg in await self._update_model_context_with_memory(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 1055, in _update_model_context_with_memory
    update_context_result = await mem.update_context(model_context)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 342, in update_context
    query_results = await self.query(query_text)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 382, in query
    self._ensure_initialized()
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 212, in _ensure_initialized
    self._client = HttpClient(
                   ^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/__init__.py", line 203, in HttpClient
    return ClientCreator(tenant=tenant, database=database, settings=settings)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 65, in __init__
    user_identity = self.get_user_identity()
                    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 103, in get_user_identity
    raise ValueError(
ValueError: Could not connect to a Chroma server. Are you sure it is running?
2025-07-01 15:10:36,863 - chat_service - ERROR - 聊天处理失败 1: Could not connect to a Chroma server. Are you sure it is running?
Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 256, in handle_request
    raise exc from None
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 101, in handle_request
    raise exc
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 78, in handle_request
    stream = self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 124, in _connect
    stream = self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_backends/sync.py", line 207, in connect_tcp
    with map_exceptions(exc_map):
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 61] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 101, in get_user_identity
    return self._server.get_user_identity()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/__init__.py", line 150, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 182, in get_user_identity
    return UserIdentity(**self._make_request("get", "/auth/identity"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 89, in _make_request
    response = self._session.request(method, url, **cast(Any, kwargs))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 825, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 914, in send
    response = self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 249, in handle_request
    with map_httpcore_exceptions():
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 61] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/client/backend/c_app/services/chat_service_v4.py", line 170, in chat_stream
    async for event in agent.run_stream(task=last_user_message):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_base_chat_agent.py", line 202, in run_stream
    async for message in self.on_messages_stream(input_messages, cancellation_token):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 951, in on_messages_stream
    for event_msg in await self._update_model_context_with_memory(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 1055, in _update_model_context_with_memory
    update_context_result = await mem.update_context(model_context)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 342, in update_context
    query_results = await self.query(query_text)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 382, in query
    self._ensure_initialized()
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 212, in _ensure_initialized
    self._client = HttpClient(
                   ^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/__init__.py", line 203, in HttpClient
    return ClientCreator(tenant=tenant, database=database, settings=settings)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 65, in __init__
    user_identity = self.get_user_identity()
                    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 103, in get_user_identity
    raise ValueError(
ValueError: Could not connect to a Chroma server. Are you sure it is running?
2025-07-01 15:11:14,083 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-01 15:11:14,083 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-01 15:11:14,083 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-01 15:11:14,547 - chat_service - ERROR - 聊天处理失败 1: Could not connect to a Chroma server. Are you sure it is running?
Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 256, in handle_request
    raise exc from None
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 101, in handle_request
    raise exc
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 78, in handle_request
    stream = self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 124, in _connect
    stream = self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_backends/sync.py", line 207, in connect_tcp
    with map_exceptions(exc_map):
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 61] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 101, in get_user_identity
    return self._server.get_user_identity()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/__init__.py", line 150, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 182, in get_user_identity
    return UserIdentity(**self._make_request("get", "/auth/identity"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 89, in _make_request
    response = self._session.request(method, url, **cast(Any, kwargs))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 825, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 914, in send
    response = self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 249, in handle_request
    with map_httpcore_exceptions():
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 61] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/client/backend/c_app/services/chat_service_v4.py", line 170, in chat_stream
    async for event in agent.run_stream(task=last_user_message):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_base_chat_agent.py", line 202, in run_stream
    async for message in self.on_messages_stream(input_messages, cancellation_token):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 951, in on_messages_stream
    for event_msg in await self._update_model_context_with_memory(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 1055, in _update_model_context_with_memory
    update_context_result = await mem.update_context(model_context)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 342, in update_context
    query_results = await self.query(query_text)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 382, in query
    self._ensure_initialized()
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 212, in _ensure_initialized
    self._client = HttpClient(
                   ^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/__init__.py", line 203, in HttpClient
    return ClientCreator(tenant=tenant, database=database, settings=settings)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 65, in __init__
    user_identity = self.get_user_identity()
                    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 103, in get_user_identity
    raise ValueError(
ValueError: Could not connect to a Chroma server. Are you sure it is running?
2025-07-01 15:11:14,547 - chat_service - ERROR - 聊天处理失败 1: Could not connect to a Chroma server. Are you sure it is running?
Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 256, in handle_request
    raise exc from None
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 101, in handle_request
    raise exc
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 78, in handle_request
    stream = self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 124, in _connect
    stream = self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_backends/sync.py", line 207, in connect_tcp
    with map_exceptions(exc_map):
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 61] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 101, in get_user_identity
    return self._server.get_user_identity()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/__init__.py", line 150, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 182, in get_user_identity
    return UserIdentity(**self._make_request("get", "/auth/identity"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 89, in _make_request
    response = self._session.request(method, url, **cast(Any, kwargs))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 825, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 914, in send
    response = self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 249, in handle_request
    with map_httpcore_exceptions():
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 61] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/client/backend/c_app/services/chat_service_v4.py", line 170, in chat_stream
    async for event in agent.run_stream(task=last_user_message):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_base_chat_agent.py", line 202, in run_stream
    async for message in self.on_messages_stream(input_messages, cancellation_token):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 951, in on_messages_stream
    for event_msg in await self._update_model_context_with_memory(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 1055, in _update_model_context_with_memory
    update_context_result = await mem.update_context(model_context)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 342, in update_context
    query_results = await self.query(query_text)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 382, in query
    self._ensure_initialized()
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 212, in _ensure_initialized
    self._client = HttpClient(
                   ^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/__init__.py", line 203, in HttpClient
    return ClientCreator(tenant=tenant, database=database, settings=settings)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 65, in __init__
    user_identity = self.get_user_identity()
                    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 103, in get_user_identity
    raise ValueError(
ValueError: Could not connect to a Chroma server. Are you sure it is running?
2025-07-01 15:11:14,547 - chat_service - ERROR - 聊天处理失败 1: Could not connect to a Chroma server. Are you sure it is running?
Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 256, in handle_request
    raise exc from None
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 101, in handle_request
    raise exc
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 78, in handle_request
    stream = self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 124, in _connect
    stream = self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_backends/sync.py", line 207, in connect_tcp
    with map_exceptions(exc_map):
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 61] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 101, in get_user_identity
    return self._server.get_user_identity()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/__init__.py", line 150, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 182, in get_user_identity
    return UserIdentity(**self._make_request("get", "/auth/identity"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 89, in _make_request
    response = self._session.request(method, url, **cast(Any, kwargs))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 825, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 914, in send
    response = self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 249, in handle_request
    with map_httpcore_exceptions():
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 61] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/client/backend/c_app/services/chat_service_v4.py", line 170, in chat_stream
    async for event in agent.run_stream(task=last_user_message):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_base_chat_agent.py", line 202, in run_stream
    async for message in self.on_messages_stream(input_messages, cancellation_token):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 951, in on_messages_stream
    for event_msg in await self._update_model_context_with_memory(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 1055, in _update_model_context_with_memory
    update_context_result = await mem.update_context(model_context)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 342, in update_context
    query_results = await self.query(query_text)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 382, in query
    self._ensure_initialized()
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 212, in _ensure_initialized
    self._client = HttpClient(
                   ^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/__init__.py", line 203, in HttpClient
    return ClientCreator(tenant=tenant, database=database, settings=settings)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 65, in __init__
    user_identity = self.get_user_identity()
                    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 103, in get_user_identity
    raise ValueError(
ValueError: Could not connect to a Chroma server. Are you sure it is running?
2025-07-01 15:14:23,368 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-01 15:14:23,369 - chat_service - INFO - Created new session 4d2b30c8-d7de-452e-8e34-e82e5aae8cdb for user 1
2025-07-01 15:16:03,079 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-01 15:16:03,080 - chat_service - INFO - Created new session 4d2b30c8-d7de-452e-8e34-e82e5aae8cdb for user 1
2025-07-01 15:31:53,818 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-01 15:31:53,820 - chat_service - INFO - Created new session 4d2b30c8-d7de-452e-8e34-e82e5aae8cdb for user 1
2025-07-01 15:31:55,337 - chat_service - ERROR - 聊天处理失败 1: Could not connect to a Chroma server. Are you sure it is running?
Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 256, in handle_request
    raise exc from None
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 101, in handle_request
    raise exc
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 78, in handle_request
    stream = self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 124, in _connect
    stream = self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_backends/sync.py", line 207, in connect_tcp
    with map_exceptions(exc_map):
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 61] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 101, in get_user_identity
    return self._server.get_user_identity()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/__init__.py", line 150, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 182, in get_user_identity
    return UserIdentity(**self._make_request("get", "/auth/identity"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 89, in _make_request
    response = self._session.request(method, url, **cast(Any, kwargs))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 825, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 914, in send
    response = self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 249, in handle_request
    with map_httpcore_exceptions():
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 61] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/client/backend/c_app/services/chat_service_v4.py", line 170, in chat_stream
    async for event in agent.run_stream(task=last_user_message):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_base_chat_agent.py", line 202, in run_stream
    async for message in self.on_messages_stream(input_messages, cancellation_token):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 951, in on_messages_stream
    for event_msg in await self._update_model_context_with_memory(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 1055, in _update_model_context_with_memory
    update_context_result = await mem.update_context(model_context)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 342, in update_context
    query_results = await self.query(query_text)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 382, in query
    self._ensure_initialized()
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 212, in _ensure_initialized
    self._client = HttpClient(
                   ^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/__init__.py", line 203, in HttpClient
    return ClientCreator(tenant=tenant, database=database, settings=settings)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 65, in __init__
    user_identity = self.get_user_identity()
                    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 103, in get_user_identity
    raise ValueError(
ValueError: Could not connect to a Chroma server. Are you sure it is running?
2025-07-10 17:53:33,031 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-10 17:53:33,032 - chat_service - INFO - Created new session 4d2b30c8-d7de-452e-8e34-e82e5aae8cdb for user 1
2025-07-10 17:53:34,509 - chat_service - ERROR - 聊天处理失败 1: Could not connect to a Chroma server. Are you sure it is running?
Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 256, in handle_request
    raise exc from None
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 101, in handle_request
    raise exc
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 78, in handle_request
    stream = self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 124, in _connect
    stream = self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_backends/sync.py", line 207, in connect_tcp
    with map_exceptions(exc_map):
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 61] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 101, in get_user_identity
    return self._server.get_user_identity()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/__init__.py", line 150, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 182, in get_user_identity
    return UserIdentity(**self._make_request("get", "/auth/identity"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 89, in _make_request
    response = self._session.request(method, url, **cast(Any, kwargs))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 825, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 914, in send
    response = self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 249, in handle_request
    with map_httpcore_exceptions():
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 61] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/client/backend/c_app/services/chat_service_v4.py", line 170, in chat_stream
    async for event in agent.run_stream(task=last_user_message):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_base_chat_agent.py", line 202, in run_stream
    async for message in self.on_messages_stream(input_messages, cancellation_token):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 951, in on_messages_stream
    for event_msg in await self._update_model_context_with_memory(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 1055, in _update_model_context_with_memory
    update_context_result = await mem.update_context(model_context)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 342, in update_context
    query_results = await self.query(query_text)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 382, in query
    self._ensure_initialized()
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 212, in _ensure_initialized
    self._client = HttpClient(
                   ^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/__init__.py", line 203, in HttpClient
    return ClientCreator(tenant=tenant, database=database, settings=settings)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 65, in __init__
    user_identity = self.get_user_identity()
                    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 103, in get_user_identity
    raise ValueError(
ValueError: Could not connect to a Chroma server. Are you sure it is running?
2025-07-10 18:06:20,799 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-10 18:06:20,800 - chat_service - INFO - Created new session 1 for user 1
2025-07-10 18:08:12,530 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-10 18:08:12,531 - chat_service - INFO - Created new session 4d2b30c8-d7de-452e-8e34-e82e5aae8cdb for user 1
2025-07-10 18:08:25,428 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-10 18:08:25,428 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-10 18:08:52,005 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-10 18:08:52,005 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-10 18:08:52,005 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-11 13:38:52,060 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-11 13:38:52,063 - chat_service - INFO - Created new session 4d2b30c8-d7de-452e-8e34-e82e5aae8cdb for user 1
2025-07-11 13:38:52,318 - chat_service - ERROR - 聊天处理失败 1: Could not connect to a Chroma server. Are you sure it is running?
Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 256, in handle_request
    raise exc from None
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 101, in handle_request
    raise exc
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 78, in handle_request
    stream = self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 124, in _connect
    stream = self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_backends/sync.py", line 207, in connect_tcp
    with map_exceptions(exc_map):
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 61] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 101, in get_user_identity
    return self._server.get_user_identity()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/__init__.py", line 150, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 182, in get_user_identity
    return UserIdentity(**self._make_request("get", "/auth/identity"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 89, in _make_request
    response = self._session.request(method, url, **cast(Any, kwargs))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 825, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 914, in send
    response = self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 249, in handle_request
    with map_httpcore_exceptions():
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 61] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/client/backend/c_app/services/chat_service_v4.py", line 170, in chat_stream
    async for event in agent.run_stream(task=last_user_message):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_base_chat_agent.py", line 202, in run_stream
    async for message in self.on_messages_stream(input_messages, cancellation_token):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 951, in on_messages_stream
    for event_msg in await self._update_model_context_with_memory(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 1055, in _update_model_context_with_memory
    update_context_result = await mem.update_context(model_context)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 342, in update_context
    query_results = await self.query(query_text)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 382, in query
    self._ensure_initialized()
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 212, in _ensure_initialized
    self._client = HttpClient(
                   ^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/__init__.py", line 203, in HttpClient
    return ClientCreator(tenant=tenant, database=database, settings=settings)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 65, in __init__
    user_identity = self.get_user_identity()
                    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 103, in get_user_identity
    raise ValueError(
ValueError: Could not connect to a Chroma server. Are you sure it is running?
2025-07-11 13:39:31,074 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-11 13:39:31,074 - chat_service - INFO - 处理用户 1 的聊天请求
2025-07-11 13:39:31,148 - chat_service - ERROR - 聊天处理失败 1: Could not connect to a Chroma server. Are you sure it is running?
Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 256, in handle_request
    raise exc from None
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 101, in handle_request
    raise exc
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 78, in handle_request
    stream = self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 124, in _connect
    stream = self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_backends/sync.py", line 207, in connect_tcp
    with map_exceptions(exc_map):
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 61] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 101, in get_user_identity
    return self._server.get_user_identity()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/__init__.py", line 150, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 182, in get_user_identity
    return UserIdentity(**self._make_request("get", "/auth/identity"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 89, in _make_request
    response = self._session.request(method, url, **cast(Any, kwargs))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 825, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 914, in send
    response = self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 249, in handle_request
    with map_httpcore_exceptions():
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 61] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/client/backend/c_app/services/chat_service_v4.py", line 170, in chat_stream
    async for event in agent.run_stream(task=last_user_message):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_base_chat_agent.py", line 202, in run_stream
    async for message in self.on_messages_stream(input_messages, cancellation_token):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 951, in on_messages_stream
    for event_msg in await self._update_model_context_with_memory(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 1055, in _update_model_context_with_memory
    update_context_result = await mem.update_context(model_context)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 342, in update_context
    query_results = await self.query(query_text)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 382, in query
    self._ensure_initialized()
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 212, in _ensure_initialized
    self._client = HttpClient(
                   ^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/__init__.py", line 203, in HttpClient
    return ClientCreator(tenant=tenant, database=database, settings=settings)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 65, in __init__
    user_identity = self.get_user_identity()
                    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 103, in get_user_identity
    raise ValueError(
ValueError: Could not connect to a Chroma server. Are you sure it is running?
2025-07-11 13:39:31,148 - chat_service - ERROR - 聊天处理失败 1: Could not connect to a Chroma server. Are you sure it is running?
Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 101, in map_httpcore_exceptions
    yield
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 250, in handle_request
    resp = self._pool.handle_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 256, in handle_request
    raise exc from None
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection_pool.py", line 236, in handle_request
    response = connection.handle_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 101, in handle_request
    raise exc
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 78, in handle_request
    stream = self._connect(request)
             ^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_sync/connection.py", line 124, in _connect
    stream = self._network_backend.connect_tcp(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_backends/sync.py", line 207, in connect_tcp
    with map_exceptions(exc_map):
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpcore/_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError: [Errno 61] Connection refused

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 101, in get_user_identity
    return self._server.get_user_identity()
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/telemetry/opentelemetry/__init__.py", line 150, in wrapper
    return f(*args, **kwargs)
           ^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 182, in get_user_identity
    return UserIdentity(**self._make_request("get", "/auth/identity"))
                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/fastapi.py", line 89, in _make_request
    response = self._session.request(method, url, **cast(Any, kwargs))
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 825, in request
    return self.send(request, auth=auth, follow_redirects=follow_redirects)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 914, in send
    response = self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 942, in _send_handling_auth
    response = self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 979, in _send_handling_redirects
    response = self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_client.py", line 1014, in _send_single_request
    response = transport.handle_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 249, in handle_request
    with map_httpcore_exceptions():
  File "/Library/Frameworks/Python.framework/Versions/3.11/lib/python3.11/contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/httpx/_transports/default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError: [Errno 61] Connection refused

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/Users/<USER>/king/ai_workspace/0507/client/backend/c_app/services/chat_service_v4.py", line 170, in chat_stream
    async for event in agent.run_stream(task=last_user_message):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_base_chat_agent.py", line 202, in run_stream
    async for message in self.on_messages_stream(input_messages, cancellation_token):
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 951, in on_messages_stream
    for event_msg in await self._update_model_context_with_memory(
                     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_agentchat/agents/_assistant_agent.py", line 1055, in _update_model_context_with_memory
    update_context_result = await mem.update_context(model_context)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 342, in update_context
    query_results = await self.query(query_text)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 382, in query
    self._ensure_initialized()
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/autogen_ext/memory/chromadb/_chromadb.py", line 212, in _ensure_initialized
    self._client = HttpClient(
                   ^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/__init__.py", line 203, in HttpClient
    return ClientCreator(tenant=tenant, database=database, settings=settings)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 65, in __init__
    user_identity = self.get_user_identity()
                    ^^^^^^^^^^^^^^^^^^^^^^^^
  File "/Users/<USER>/king/ai_workspace/0507/venv/lib/python3.11/site-packages/chromadb/api/client.py", line 103, in get_user_identity
    raise ValueError(
ValueError: Could not connect to a Chroma server. Are you sure it is running?
