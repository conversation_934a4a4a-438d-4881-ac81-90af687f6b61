{"name": "admin-frontend", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "lint": "eslint --ext .js,.vue .", "lint:fix": "eslint --fix --ext .js,.vue .", "lint:staged": "lint-staged", "prettier": "npx prettier --write ."}, "dependencies": {"@iconify/json": "^2.2.228", "@iconify/vue": "^4.1.1", "@unocss/eslint-config": "^0.55.0", "@vueuse/core": "^10.3.0", "@zclzone/eslint-config": "^0.0.4", "axios": "^1.4.0", "dayjs": "^1.11.9", "dotenv": "^16.3.1", "eslint": "^8.46.0", "lodash-es": "^4.17.21", "naive-ui": "^2.34.4", "pinia": "^2.1.6", "rollup-plugin-visualizer": "^5.9.2", "sass": "^1.65.1", "typescript": "^5.1.6", "unocss": "^0.55.0", "unplugin-auto-import": "^0.16.6", "unplugin-icons": "^0.16.5", "unplugin-vue-components": "^0.25.1", "vite-plugin-compression": "^0.5.1", "vite-plugin-html": "^3.2.0", "vite-plugin-svg-icons": "^2.0.1", "vue": "^3.3.4", "vue-i18n": "9", "vue-router": "^4.2.4"}, "devDependencies": {"@vitejs/plugin-vue": "^4.6.2", "vite": "^4.4.6"}, "lint-staged": {"*.{js,vue}": ["eslint --ext .js,.vue ."]}, "eslintConfig": {"extends": ["@zclzone", "@unocss", ".eslint-global-variables.json"]}}